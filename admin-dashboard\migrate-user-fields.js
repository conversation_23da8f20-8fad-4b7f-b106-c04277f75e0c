#!/usr/bin/env node

/**
 * 岱宗文脉 - 用户数据字段顺序迁移脚本
 * 重新组织用户数据的字段顺序，将id和username放在最前面
 */

import cloudbase from '@cloudbase/node-sdk';

// 初始化CloudBase
const app = cloudbase.init({
  env: 'novel-app-2gywkgnn15cbd6a8'
});

const db = app.database();

async function migrateUserFields() {
  console.log('🔄 开始迁移用户数据字段顺序...');

  try {
    // 获取所有用户数据
    const usersResult = await db.collection('users').get();
    const users = usersResult.data || [];

    console.log(`📊 找到 ${users.length} 个用户需要迁移`);

    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      console.log(`\n🔄 处理用户 ${i + 1}/${users.length}: ${user.username || user.id}`);

      // 重新组织用户数据，按照优化的字段顺序
      const optimizedUser = {
        // 核心标识字段放在最前面
        id: user.id,
        username: user.username,
        
        // 联系信息
        phoneNumber: user.phoneNumber,
        email: user.email || null,
        
        // 用户资料
        avatar: user.avatar || null,
        passwordHash: user.passwordHash,
        
        // 会员信息
        isMember: user.isMember || false,
        isPermanentMember: user.isPermanentMember || false,
        membershipType: user.membershipType || 'none',
        memberExpireTime: user.memberExpireTime || null,
        memberCode: user.memberCode || null,
        
        // 系统设置
        isDataSyncEnabled: user.isDataSyncEnabled !== false, // 默认true
        settings: user.settings || {
          autoSync: true,
          enableBiometric: false,
          enableNotification: true,
          language: 'zh-CN',
          theme: 'system'
        },
        
        // 时间戳
        createdAt: user.createdAt,
        updatedAt: new Date().toISOString()
      };

      // 更新用户数据（使用set替换整个文档，保持字段顺序）
      try {
        await db.collection('users').doc(user._id).set(optimizedUser);
        console.log(`✅ 用户 ${user.username || user.id} 迁移成功`);
      } catch (error) {
        console.error(`❌ 用户 ${user.username || user.id} 迁移失败:`, error.message);
      }
    }

    console.log('\n🎉 用户数据字段顺序迁移完成！');
    console.log('\n📋 迁移后的字段顺序:');
    console.log('   1. id (用户ID) ← 最前面');
    console.log('   2. username (用户名) ← 最前面');
    console.log('   3. phoneNumber (手机号)');
    console.log('   4. email (邮箱)');
    console.log('   5. avatar (头像)');
    console.log('   6. passwordHash (密码哈希)');
    console.log('   7. isMember (是否会员)');
    console.log('   8. isPermanentMember (是否永久会员)');
    console.log('   9. membershipType (会员类型)');
    console.log('   10. memberExpireTime (会员过期时间)');
    console.log('   11. memberCode (会员码)');
    console.log('   12. isDataSyncEnabled (数据同步开关)');
    console.log('   13. settings (用户设置)');
    console.log('   14. createdAt (创建时间)');
    console.log('   15. updatedAt (更新时间)');
    console.log('\n⚠️  注意: _id 字段是CloudBase系统字段，无法删除，但不会在API中显示');

  } catch (error) {
    console.error('❌ 迁移失败:', error);
    process.exit(1);
  }
}

// 运行迁移
if (import.meta.url === `file://${process.argv[1]}`) {
  migrateUserFields()
    .then(() => {
      console.log('\n✨ 迁移脚本执行完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 迁移脚本执行失败:', error);
      process.exit(1);
    });
}

export { migrateUserFields };
