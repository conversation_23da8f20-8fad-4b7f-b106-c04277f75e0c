import 'dart:convert';
import 'dart:math' as Math;
import 'package:dio/dio.dart' as dio_pkg;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import '../models/user.dart';
import '../models/novel.dart';
import '../models/character_card.dart';
import '../models/character_type.dart';
import '../models/knowledge_document.dart';
import '../models/writing_style_package.dart';
import '../config/api_config.dart';
import '../controllers/novel_controller.dart';
import '../controllers/knowledge_base_controller.dart';
import '../controllers/writing_style_package_controller.dart';
import '../controllers/character_card_controller.dart';
import '../controllers/character_type_controller.dart';
import '../controllers/style_controller.dart';
import 'auth_service.dart';
import 'cloudbase_direct_upload_service.dart';
import 'cloudbase_sync_service.dart';
import 'hybrid_sync_service.dart';

/// 用户数据同步服务
class UserSyncService extends GetxService {
  static const String _syncEnabledKey = 'sync_enabled';
  static const String _lastSyncTimeKey = 'last_sync_time';

  final dio_pkg.Dio _dio = dio_pkg.Dio();
  final AuthService _authService = Get.find<AuthService>();
  
  final RxBool isSyncEnabled = true.obs;
  final RxBool isSyncing = false.obs;
  final Rx<DateTime?> lastSyncTime = Rx<DateTime?>(null);

  SharedPreferences? _prefs;

  @override
  Future<void> onInit() async {
    super.onInit();
    _prefs = await SharedPreferences.getInstance();
    _loadSyncSettings();
    
    // 监听用户登录状态变化
    ever(_authService.isLoggedIn, (isLoggedIn) {
      if (isLoggedIn && isSyncEnabled.value) {
        // 用户登录后自动同步
        syncUserData();
      }
    });
  }

  /// 加载同步设置
  void _loadSyncSettings() {
    isSyncEnabled.value = _prefs?.getBool(_syncEnabledKey) ?? true;
    final lastSyncStr = _prefs?.getString(_lastSyncTimeKey);
    if (lastSyncStr != null) {
      lastSyncTime.value = DateTime.parse(lastSyncStr);
    }
  }

  /// 设置同步开关
  Future<void> setSyncEnabled(bool enabled) async {
    // 检查用户是否为会员
    final user = _authService.currentUser.value;
    if (enabled && (user == null || !user.isValidMember)) {
      Get.snackbar('权限不足', '数据同步功能仅限会员使用');
      return;
    }

    isSyncEnabled.value = enabled;
    await _prefs?.setBool(_syncEnabledKey, enabled);

    if (enabled && _authService.isLoggedIn.value) {
      // 启用同步时立即同步一次
      await syncUserData();
    }
  }

  /// 同步用户数据
  Future<bool> syncUserData() async {
    if (!_authService.isLoggedIn.value || !isSyncEnabled.value) {
      return false;
    }

    // 检查用户是否为会员，非会员不能使用数据同步
    final user = _authService.currentUser.value;
    if (user == null || !user.isValidMember) {
      print('数据同步失败: 非会员用户无法使用数据同步功能');
      Get.snackbar('权限不足', '数据同步功能仅限会员使用');
      return false;
    }

    try {
      isSyncing.value = true;
      
      // 获取本地数据
      final localData = await _collectLocalData();
      
      // 获取认证Token
      final token = await _getToken();
      if (token == null) {
        print('同步失败: 未找到认证Token');
        return false;
      }

      // 使用分批上传到服务器
      final success = await _uploadDataInBatches(localData, token);

      if (success) {
        // 下载服务器数据
        await _downloadServerData();
        
        // 更新最后同步时间
        lastSyncTime.value = DateTime.now();
        await _prefs?.setString(_lastSyncTimeKey, lastSyncTime.value!.toIso8601String());
        
        // 显示同步成功的详细信息
        final localData = await _collectLocalData();
        final novelsCount = (localData['novels'] as List?)?.length ?? 0;
        final charactersCount = (localData['characterCards'] as List?)?.length ?? 0;
        final typesCount = (localData['characterTypes'] as List?)?.length ?? 0;
        final docsCount = (localData['knowledgeDocuments'] as List?)?.length ?? 0;
        final stylesCount = (localData['stylePackages'] as List?)?.length ?? 0;

        final syncSummary = [
          if (novelsCount > 0) '$novelsCount本小说',
          if (charactersCount > 0) '$charactersCount个角色',
          if (typesCount > 0) '$typesCount个类型',
          if (docsCount > 0) '$docsCount个文档',
          if (stylesCount > 0) '$stylesCount个风格包',
        ].join('，');

        final message = syncSummary.isNotEmpty ? '数据同步完成：$syncSummary' : '数据同步完成';
        Get.snackbar('成功', message);
        return true;
      } else {
        Get.snackbar('错误', '数据同步失败');
        return false;
      }
    } catch (e) {
      print('数据同步失败: $e');
      Get.snackbar('错误', '数据同步失败: ${e.toString()}');
      return false;
    } finally {
      isSyncing.value = false;
    }
  }

  /// 收集本地数据
  Future<Map<String, dynamic>> _collectLocalData() async {
    final data = <String, dynamic>{};
    
    try {
      // 收集小说数据
      try {
        print('🔍 检查NovelController是否注册: ${Get.isRegistered<NovelController>()}');
        if (Get.isRegistered<NovelController>()) {
          final novelController = Get.find<NovelController>();
          print('📚 NovelController找到，小说数量: ${novelController.novels.length}');

          // 如果控制器中没有数据，尝试重新加载
          if (novelController.novels.isEmpty) {
            print('! NovelController中没有数据，尝试重新加载...');
            try {
              await novelController.loadNovels(); // 这是异步方法，需要await
              print('🔄 重新加载后小说数量: ${novelController.novels.length}');

              // 等待一小段时间确保数据完全加载
              await Future.delayed(Duration(milliseconds: 500));
              print('🔄 延迟后小说数量: ${novelController.novels.length}');

              // 如果还是没有数据，尝试强制刷新
              if (novelController.novels.isEmpty) {
                print('🔄 尝试强制刷新数据...');
                novelController.novels.refresh();
                await Future.delayed(Duration(milliseconds: 300));
                print('🔄 强制刷新后小说数量: ${novelController.novels.length}');
              }
            } catch (e) {
              print('❌ 重新加载小说失败: $e');
            }
          }

          final novelsData = novelController.novels.map((novel) => novel.toJson()).toList();
          data['novels'] = novelsData;
          print('✅ 收集到 ${novelsData.length} 本小说数据');

          // 打印前几本小说的标题用于调试
          if (novelsData.isNotEmpty) {
            print('   前几本小说:');
            for (int i = 0; i < Math.min(3, novelsData.length); i++) {
              print('   ${i + 1}. ${novelsData[i]['title']}');
            }
          } else {
            print('⚠️ 警告：应用中有19本小说，但NovelController中为空！');
            print('   这可能是数据加载时机问题');
          }
        } else {
          print('❌ NovelController未注册');
          data['novels'] = [];
        }
      } catch (e) {
        print('❌ 收集小说数据失败: $e');
        data['novels'] = [];
      }

      // 收集角色卡片数据
      try {
        print('🔍 检查CharacterCardController是否注册: ${Get.isRegistered<CharacterCardController>()}');
        if (Get.isRegistered<CharacterCardController>()) {
          final characterCardController = Get.find<CharacterCardController>();
          print('👥 CharacterCardController找到，角色卡片数量: ${characterCardController.characterCards.length}');

          // 如果控制器中没有数据，尝试重新加载
          if (characterCardController.characterCards.isEmpty) {
            print('⚠️ CharacterCardController中没有数据，尝试重新加载...');
            try {
              characterCardController.loadCharacterCards();
              print('🔄 重新加载后角色卡片数量: ${characterCardController.characterCards.length}');
            } catch (e) {
              print('❌ 重新加载角色卡片失败: $e');
            }
          }

          final characterCards = characterCardController.characterCards
              .map((card) => card.toJson())
              .toList();

          data['characterCards'] = characterCards;
          print('✅ 收集到 ${characterCards.length} 个角色卡片');

          // 打印前几个角色卡片的名称用于调试
          if (characterCards.isNotEmpty) {
            print('   前几个角色卡片:');
            for (int i = 0; i < Math.min(3, characterCards.length); i++) {
              print('   ${i + 1}. ${characterCards[i]['name']}');
            }
          }
        } else {
          data['characterCards'] = [];
          print('❌ 角色卡片控制器未初始化，跳过收集');
        }
      } catch (e) {
        print('❌ 收集角色卡片数据失败: $e');
        data['characterCards'] = [];
      }

      // 收集自定义角色类型
      try {
        print('🔍 检查CharacterTypeController是否注册: ${Get.isRegistered<CharacterTypeController>()}');
        if (Get.isRegistered<CharacterTypeController>()) {
          final characterTypeController = Get.find<CharacterTypeController>();
          print('🏷️ CharacterTypeController找到，角色类型数量: ${characterTypeController.characterTypes.length}');

          // 如果控制器中没有数据，尝试重新加载
          if (characterTypeController.characterTypes.isEmpty) {
            print('⚠️ CharacterTypeController中没有数据，尝试重新加载...');
            try {
              characterTypeController.loadCharacterTypes();
              print('🔄 重新加载后角色类型数量: ${characterTypeController.characterTypes.length}');
            } catch (e) {
              print('❌ 重新加载角色类型失败: $e');
            }
          }

          final characterTypes = characterTypeController.characterTypes
              .map((type) => type.toJson())
              .toList();
          data['characterTypes'] = characterTypes;
          print('✅ 收集到 ${characterTypes.length} 个角色类型');

          // 打印前几个角色类型的名称用于调试
          if (characterTypes.isNotEmpty) {
            print('   前几个角色类型:');
            for (int i = 0; i < Math.min(3, characterTypes.length); i++) {
              print('   ${i + 1}. ${characterTypes[i]['name']}');
            }
          }
        } else {
          data['characterTypes'] = [];
          print('❌ 角色类型控制器未初始化，跳过收集');
        }
      } catch (e) {
        print('❌ 收集角色类型数据失败: $e');
        data['characterTypes'] = [];
      }

      // 收集知识库文档（排除官方只读内容）
      try {
        final knowledgeController = Get.find<KnowledgeBaseController>();

        // 过滤掉官方只读文档
        final userDocuments = knowledgeController.documents.where((doc) {
          // 排除只读文档
          if (doc.isReadOnly) return false;

          // 排除包含"官方"标签的文档
          if (doc.tags.contains('官方')) return false;

          // 排除默认文档
          if (doc.isDefault) return false;

          return true;
        }).toList();

        data['knowledgeDocuments'] = userDocuments.map((doc) => doc.toJson()).toList();
        print('收集到 ${userDocuments.length} 个用户知识库文档（已排除 ${knowledgeController.documents.length - userDocuments.length} 个官方文档）');
      } catch (e) {
        print('收集知识库数据失败: $e');
        data['knowledgeDocuments'] = [];
      }

      // 收集写作风格包
      try {
        final styleController = Get.find<WritingStylePackageController>();
        data['stylePackages'] = styleController.packages.map((pkg) => pkg.toJson()).toList();
      } catch (e) {
        print('收集风格包数据失败: $e');
        data['stylePackages'] = [];
      }

      // 收集用户设置
      try {
        print('🔍 检查用户设置...');
        final currentUser = _authService.currentUser.value;
        if (currentUser != null && currentUser.settings != null) {
          data['userSettings'] = currentUser.settings.toJson();
          print('✅ 收集到用户设置数据');
        } else {
          data['userSettings'] = null;
          print('❌ 用户设置为空');
        }
      } catch (e) {
        print('❌ 收集用户设置失败: $e');
        data['userSettings'] = null;
      }

    } catch (e) {
      print('收集本地数据失败: $e');
    }
    
    return data;
  }

  /// 从云端下载数据（公开方法，供登录后调用）
  Future<bool> downloadFromCloud() async {
    if (!_authService.isLoggedIn.value) {
      print('下载失败: 用户未登录');
      return false;
    }

    // 检查用户是否为会员
    final user = _authService.currentUser.value;
    if (user == null || !user.isValidMember) {
      print('下载失败: 非会员用户无法使用数据同步功能');
      return false;
    }

    // 确保必要的控制器已注册
    _ensureControllersRegistered();

    try {
      isSyncing.value = true;
      print('📥 开始从云端下载数据...');

      final token = await _getToken();
      if (token == null) {
        print('下载失败: 未找到认证Token');
        return false;
      }

      // 使用新的混合同步服务下载数据
      final hybridSyncService = Get.find<HybridSyncService>();

      // 检查服务是否已初始化
      if (!hybridSyncService.isInitialized.value) {
        print('❌ 混合同步服务未初始化');
        Get.snackbar('同步失败', '同步服务未就绪，请稍后重试');
        return false;
      }

      // 暂时返回false，因为下载功能需要重新实现
      print('ℹ️ 下载功能正在重构中，请使用上传同步');
      Get.snackbar('功能提示', '下载功能正在重构中，请使用上传同步');
      return false;
    } catch (e) {
      print('下载云端数据失败: $e');
      Get.snackbar('同步失败', '网络错误，请稍后重试');
      return false;
    } finally {
      isSyncing.value = false;
    }
  }

  /// 下载服务器数据（内部方法）
  Future<void> _downloadServerData() async {
    await downloadFromCloud();
  }

  /// 应用服务器数据
  Future<void> _applyServerData(Map<String, dynamic> serverData) async {
    try {
      // 应用小说数据
      if (serverData['novels'] != null) {
        try {
          final novelController = Get.find<NovelController>();
          final serverNovels = (serverData['novels'] as List)
              .map((json) {
                try {
                  // 确保必要字段不为null
                  if (json['id'] == null) json['id'] = 'unknown_${DateTime.now().millisecondsSinceEpoch}';
                  if (json['title'] == null) json['title'] = '未命名小说';
                  if (json['author'] == null) json['author'] = '未知作者';
                  if (json['genre'] == null) json['genre'] = '其他';
                  if (json['outline'] == null) json['outline'] = '暂无大纲';
                  if (json['content'] == null) json['content'] = '';
                  if (json['chapters'] == null) json['chapters'] = [];
                  if (json['createdAt'] == null) json['createdAt'] = DateTime.now().toIso8601String();

                  return Novel.fromJson(json);
                } catch (e) {
                  print('⚠️ 跳过无效小说数据: $e');
                  return null;
                }
              })
              .where((novel) => novel != null)
              .cast<Novel>()
              .toList();
          await _mergeNovels(novelController, serverNovels);
          print('✅ 应用了 ${serverNovels.length} 个小说数据');
        } catch (e) {
          print('❌ 应用小说数据失败: $e');
        }
      }

      // 应用角色卡片数据
      if (serverData['characterCards'] != null) {
        try {
          if (Get.isRegistered<CharacterCardController>()) {
            final characterCardController = Get.find<CharacterCardController>();
            final serverCards = (serverData['characterCards'] as List)
                .map((json) => CharacterCard.fromJson(json))
                .toList();
            await _mergeCharacterCards(characterCardController, serverCards);
            print('✅ 应用了 ${serverCards.length} 个角色卡片数据');
          } else {
            print('⚠️ 角色卡片控制器未初始化，跳过应用角色卡片数据');
          }
        } catch (e) {
          print('❌ 应用角色卡片数据失败: $e');
        }
      }

      // 应用自定义角色类型
      if (serverData['characterTypes'] != null) {
        try {
          if (Get.isRegistered<CharacterTypeController>()) {
            final characterTypeController = Get.find<CharacterTypeController>();
            final serverTypes = (serverData['characterTypes'] as List)
                .map((json) => CharacterType.fromJson(json))
                .toList();
            await _mergeCharacterTypes(characterTypeController, serverTypes);
            print('✅ 应用了 ${serverTypes.length} 个角色类型数据');
          } else {
            print('⚠️ 角色类型控制器未初始化，跳过应用角色类型数据');
          }
        } catch (e) {
          print('❌ 应用角色类型数据失败: $e');
        }
      }

      // 应用知识库文档
      if (serverData['knowledgeDocuments'] != null) {
        try {
          final knowledgeController = Get.find<KnowledgeBaseController>();
          final serverDocs = (serverData['knowledgeDocuments'] as List)
              .map((json) {
                try {
                  // 确保必要字段不为null
                  if (json['id'] == null) json['id'] = 'unknown_${DateTime.now().millisecondsSinceEpoch}';
                  if (json['title'] == null) json['title'] = '未命名文档';
                  if (json['content'] == null) json['content'] = '';
                  if (json['category'] == null) json['category'] = '未分类';
                  if (json['tags'] == null) json['tags'] = [];
                  if (json['createdAt'] == null) json['createdAt'] = DateTime.now().toIso8601String();
                  if (json['updatedAt'] == null) json['updatedAt'] = DateTime.now().toIso8601String();

                  return KnowledgeDocument.fromJson(json);
                } catch (e) {
                  print('⚠️ 跳过无效知识库文档: $e');
                  return null;
                }
              })
              .where((doc) => doc != null)
              .cast<KnowledgeDocument>()
              .toList();
          await _mergeKnowledgeDocuments(knowledgeController, serverDocs);
          print('✅ 应用了 ${serverDocs.length} 个知识库文档');
        } catch (e) {
          print('❌ 应用知识库数据失败: $e');
        }
      }

      // 应用写作风格包
      if (serverData['stylePackages'] != null) {
        try {
          final styleController = Get.find<WritingStylePackageController>();
          final serverPackages = (serverData['stylePackages'] as List)
              .map((json) => WritingStylePackage.fromJson(json))
              .toList();
          await _mergeStylePackages(styleController, serverPackages);
        } catch (e) {
          print('应用风格包数据失败: $e');
        }
      }
      
      // 应用用户设置
      if (serverData['userSettings'] != null) {
        final settings = UserSettings.fromJson(serverData['userSettings']);
        if (_authService.currentUser.value != null) {
          _authService.currentUser.value!.settings = settings;
          // 保存到本地
          await _prefs?.setString('user_data', jsonEncode(_authService.currentUser.value!.toJson()));
        }
      }
      
    } catch (e) {
      print('应用服务器数据失败: $e');
    }
  }

  /// 合并小说数据
  Future<void> _mergeNovels(NovelController controller, List<Novel> serverNovels) async {
    // 实现数据合并逻辑，以最新更新时间为准
    for (final serverNovel in serverNovels) {
      final localIndex = controller.novels.indexWhere((n) => n.id == serverNovel.id);
      if (localIndex == -1) {
        // 本地没有，直接添加
        controller.novels.add(serverNovel);
      } else {
        // 本地有，比较更新时间
        final localNovel = controller.novels[localIndex];
        if (serverNovel.updatedAt != null &&
            (localNovel.updatedAt == null || serverNovel.updatedAt!.isAfter(localNovel.updatedAt!))) {
          controller.novels[localIndex] = serverNovel;
        }
      }
    }
    // 保存小说数据
    try {
      final novelController = Get.find<NovelController>();
      // NovelController没有saveAllNovels方法，但小说数据已经自动保存到Hive
      print('小说数据已自动保存到本地存储');
    } catch (e) {
      print('保存小说数据失败: $e');
    }
  }

  /// 合并角色卡片数据
  Future<void> _mergeCharacterCards(CharacterCardController controller, List<CharacterCard> serverCards) async {
    try {
      for (final serverCard in serverCards) {
        final localIndex = controller.characterCards.indexWhere((c) => c.id == serverCard.id);
        if (localIndex == -1) {
          // 本地没有，直接添加
          controller.characterCards.add(serverCard);
          print('添加新角色卡片: ${serverCard.name}');
        } else {
          // 本地有，直接替换（因为CharacterCard没有updatedAt字段）
          controller.characterCards[localIndex] = serverCard;
          print('更新角色卡片: ${serverCard.name}');
        }
      }
      // 触发UI更新
      controller.characterCards.refresh();
      print('角色卡片数据已应用到控制器');
    } catch (e) {
      print('角色卡片数据合并失败: $e');
    }
  }

  /// 合并角色类型数据
  Future<void> _mergeCharacterTypes(CharacterTypeController controller, List<CharacterType> serverTypes) async {
    try {
      for (final serverType in serverTypes) {
        final localIndex = controller.characterTypes.indexWhere((t) => t.id == serverType.id);
        if (localIndex == -1) {
          // 本地没有，直接添加
          controller.characterTypes.add(serverType);
          print('添加新角色类型: ${serverType.name}');
        } else {
          // 本地有，直接替换（因为CharacterType没有updatedAt字段）
          controller.characterTypes[localIndex] = serverType;
          print('更新角色类型: ${serverType.name}');
        }
      }
      // 触发UI更新
      controller.characterTypes.refresh();
      print('角色类型数据已应用到控制器');
    } catch (e) {
      print('角色类型数据合并失败: $e');
    }
  }

  /// 合并知识库文档数据
  Future<void> _mergeKnowledgeDocuments(KnowledgeBaseController controller, List<KnowledgeDocument> serverDocs) async {
    for (final serverDoc in serverDocs) {
      final localIndex = controller.documents.indexWhere((d) => d.id == serverDoc.id);
      if (localIndex == -1) {
        controller.documents.add(serverDoc);
      } else {
        final localDoc = controller.documents[localIndex];
        if (serverDoc.updatedAt.isAfter(localDoc.updatedAt)) {
          controller.documents[localIndex] = serverDoc;
        }
      }
    }
    // 保存知识库数据
    try {
      // 知识库数据已经在上面的循环中逐个处理了
      print('知识库数据同步完成');
    } catch (e) {
      print('保存知识库数据失败: $e');
    }
  }

  /// 合并写作风格包数据
  Future<void> _mergeStylePackages(WritingStylePackageController controller, List<WritingStylePackage> serverPackages) async {
    for (final serverPackage in serverPackages) {
      final localIndex = controller.packages.indexWhere((p) => p.id == serverPackage.id);
      if (localIndex == -1) {
        controller.packages.add(serverPackage);
      } else {
        controller.packages[localIndex] = serverPackage;
      }
    }
    // 保存风格包数据
    try {
      if (Get.isRegistered<StyleController>()) {
        final styleController = Get.find<StyleController>();
        // StyleController的数据已经自动保存到SharedPreferences
        print('风格包数据已自动保存到本地存储');
      } else {
        print('风格包控制器未初始化，跳过保存');
      }
    } catch (e) {
      print('保存风格包数据失败: $e');
    }
  }



  /// 导出用户数据
  Future<Map<String, dynamic>> exportUserData() async {
    return await _collectLocalData();
  }

  /// 导入用户数据
  Future<bool> importUserData(Map<String, dynamic> data) async {
    try {
      await _applyServerData(data);
      Get.snackbar('成功', '数据导入完成');
      return true;
    } catch (e) {
      print('数据导入失败: $e');
      Get.snackbar('错误', '数据导入失败: ${e.toString()}');
      return false;
    }
  }

  /// 检查本地数据状态
  Future<Map<String, dynamic>> checkLocalDataStatus() async {
    final status = <String, dynamic>{};

    try {
      // 深度检查小说数据（Hive存储）
      if (Get.isRegistered<NovelController>()) {
        final novelController = Get.find<NovelController>();

        print('🔍 深度数据检查（Hive存储）:');

        // 确保数据已加载
        await novelController.loadNovels();

        // 获取控制器中的数据
        final controllerNovels = novelController.novels;
        print('   NovelController中的小说数量: ${controllerNovels.length}');

        // 获取小说标题示例
        final titles = controllerNovels.take(3).map((n) => n.title).toList();
        final sampleData = <String, dynamic>{};

        // 收集示例数据
        for (int i = 0; i < Math.min(3, controllerNovels.length); i++) {
          final novel = controllerNovels[i];
          sampleData['novel_${i + 1}'] = {
            'title': novel.title,
            'id': novel.id,
            'content_length': novel.content.length,
            'chapters_count': novel.chapters.length,
            'created_at': novel.createdAt.toString(),
          };
        }

        status['novels'] = {
          'controller_count': controllerNovels.length,
          'storage_type': 'Hive',
          'loaded': controllerNovels.isNotEmpty,
          'titles': titles,
          'sample_data': sampleData,
          'data_source': 'NovelController (Hive)',
          'data_available': controllerNovels.isNotEmpty,
        };

        print('   小说标题示例: ${titles.join(', ')}');
      } else {
        status['novels'] = {'count': 0, 'loaded': false, 'error': 'Controller not registered'};
      }

      // 检查角色卡片数据
      if (Get.isRegistered<CharacterCardController>()) {
        final characterController = Get.find<CharacterCardController>();
        status['characters'] = {
          'count': characterController.characterCards.length,
          'loaded': characterController.characterCards.isNotEmpty,
        };
      } else {
        status['characters'] = {'count': 0, 'loaded': false, 'error': 'Controller not registered'};
      }

      // 检查知识库数据
      if (Get.isRegistered<KnowledgeBaseController>()) {
        final knowledgeController = Get.find<KnowledgeBaseController>();
        final userDocs = knowledgeController.documents.where((doc) => !doc.isDefault).toList();
        status['knowledge'] = {
          'count': userDocs.length,
          'loaded': userDocs.isNotEmpty,
        };
      } else {
        status['knowledge'] = {'count': 0, 'loaded': false, 'error': 'Controller not registered'};
      }

      // 检查用户信息
      final user = _authService.currentUser.value;
      status['user'] = {
        'logged_in': user != null,
        'username': user?.username,
        'is_member': user?.isValidMember ?? false,
        'sync_enabled': isSyncEnabled.value,
      };

      // 检查同步状态
      status['sync'] = {
        'enabled': isSyncEnabled.value,
        'last_sync': lastSyncTime.value?.toIso8601String(),
        'is_syncing': isSyncing.value,
      };

    } catch (e) {
      status['error'] = e.toString();
    }

    return status;
  }

  /// 显示数据状态报告
  Future<void> showDataStatusReport() async {
    final status = await checkLocalDataStatus();

    final buffer = StringBuffer();
    buffer.writeln('📊 本地数据状态报告\n');

    // 用户状态
    final user = status['user'] as Map<String, dynamic>;
    buffer.writeln('👤 用户状态:');
    buffer.writeln('   登录状态: ${user['logged_in'] ? '✅ 已登录' : '❌ 未登录'}');
    if (user['logged_in']) {
      buffer.writeln('   用户名: ${user['username']}');
      buffer.writeln('   会员状态: ${user['is_member'] ? '✅ 会员' : '❌ 非会员'}');
      buffer.writeln('   同步启用: ${user['sync_enabled'] ? '✅ 已启用' : '❌ 未启用'}');
    }
    buffer.writeln('');

    // 小说数据
    final novels = status['novels'] as Map<String, dynamic>;
    buffer.writeln('📚 小说数据:');
    buffer.writeln('   数量: ${novels['controller_count'] ?? 0} 本');
    buffer.writeln('   存储类型: ${novels['storage_type'] ?? 'Unknown'}');
    buffer.writeln('   数据源: ${novels['data_source'] ?? 'Unknown'}');
    buffer.writeln('   状态: ${novels['loaded'] ? '✅ 已加载' : '❌ 未加载'}');
    buffer.writeln('   数据可用: ${novels['data_available'] ? '✅ 是' : '❌ 否'}');

    if (novels['titles'] != null && (novels['titles'] as List).isNotEmpty) {
      buffer.writeln('   小说示例: ${(novels['titles'] as List).join(', ')}');
    }

    if (novels['sample_data'] != null) {
      final sampleData = novels['sample_data'] as Map<String, dynamic>;
      buffer.writeln('   详细信息:');
      sampleData.forEach((key, data) {
        if (data['title'] != null) {
          buffer.writeln('     ${data['title']}:');
          buffer.writeln('       - 内容长度: ${data['content_length']} 字');
          buffer.writeln('       - 章节数: ${data['chapters_count']}');
          buffer.writeln('       - 创建时间: ${data['created_at']}');
        }
      });
    }

    if (novels['error'] != null) {
      buffer.writeln('   错误: ${novels['error']}');
    }
    buffer.writeln('');

    // 角色数据
    final characters = status['characters'] as Map<String, dynamic>;
    buffer.writeln('👥 角色数据:');
    buffer.writeln('   数量: ${characters['count']} 个');
    buffer.writeln('   状态: ${characters['loaded'] ? '✅ 已加载' : '❌ 未加载'}');
    if (characters['error'] != null) {
      buffer.writeln('   错误: ${characters['error']}');
    }
    buffer.writeln('');

    // 知识库数据
    final knowledge = status['knowledge'] as Map<String, dynamic>;
    buffer.writeln('📖 知识库数据:');
    buffer.writeln('   数量: ${knowledge['count']} 个');
    buffer.writeln('   状态: ${knowledge['loaded'] ? '✅ 已加载' : '❌ 未加载'}');
    if (knowledge['error'] != null) {
      buffer.writeln('   错误: ${knowledge['error']}');
    }
    buffer.writeln('');

    // 同步状态
    final sync = status['sync'] as Map<String, dynamic>;
    buffer.writeln('🔄 同步状态:');
    buffer.writeln('   同步启用: ${sync['enabled'] ? '✅ 已启用' : '❌ 未启用'}');
    buffer.writeln('   最后同步: ${sync['last_sync'] ?? '从未同步'}');
    buffer.writeln('   正在同步: ${sync['is_syncing'] ? '✅ 是' : '❌ 否'}');

    Get.dialog(
      AlertDialog(
        title: Text('数据状态报告'),
        content: SingleChildScrollView(
          child: Text(
            buffer.toString(),
            style: TextStyle(fontFamily: 'monospace', fontSize: 12),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('确定'),
          ),
          if (!sync['enabled'])
            TextButton(
              onPressed: () {
                Get.back();
                _enableSync();
              },
              child: Text('启用同步'),
            ),
          if (sync['enabled'] && !sync['is_syncing'])
            TextButton(
              onPressed: () {
                Get.back();
                syncUserData();
              },
              child: Text('立即同步'),
            ),
          // 如果数据可用但未同步，提供强制同步选项
          if (status['novels'] != null &&
              (status['novels'] as Map)['data_available'] == true &&
              sync['enabled'] && !sync['is_syncing'])
            TextButton(
              onPressed: () {
                Get.back();
                syncUserData();
              },
              child: Text('强制同步'),
            ),
        ],
      ),
    );
  }

  /// 启用数据同步
  void _enableSync() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_syncEnabledKey, true);
      isSyncEnabled.value = true;
      Get.snackbar('成功', '数据同步已启用');
    } catch (e) {
      Get.snackbar('错误', '启用同步失败: $e');
    }
  }

  /// 修复数据加载问题
  void _fixDataLoadingIssue() async {
    Get.dialog(
      AlertDialog(
        title: Text('修复数据加载'),
        content: Text('正在尝试修复数据加载问题，这可能需要几秒钟...'),
      ),
      barrierDismissible: false,
    );

    try {
      print('🔧 开始修复数据加载问题...');

      // 1. 重新初始化NovelController
      if (Get.isRegistered<NovelController>()) {
        final novelController = Get.find<NovelController>();
        print('🔧 清空NovelController数据...');
        novelController.novels.clear();

        // 2. 强制重新加载
        print('🔧 强制重新加载小说数据...');
        await novelController.loadNovels();

        // 3. 如果还是没有数据，尝试直接从SharedPreferences读取
        if (novelController.novels.isEmpty) {
          print('🔧 尝试直接从SharedPreferences读取数据...');
          await _directLoadFromStorage(novelController);
        }

        print('🔧 修复完成，当前小说数量: ${novelController.novels.length}');
      }

      // 4. 同样处理角色数据
      if (Get.isRegistered<CharacterCardController>()) {
        final characterController = Get.find<CharacterCardController>();
        print('🔧 重新加载角色数据...');
        characterController.loadCharacterCards();
        print('🔧 当前角色数量: ${characterController.characterCards.length}');
      }

      Get.back(); // 关闭加载对话框

      // 显示结果
      final novelController = Get.find<NovelController>();
      if (novelController.novels.isNotEmpty) {
        Get.snackbar('成功', '数据修复完成！找到 ${novelController.novels.length} 本小说');
      } else {
        Get.snackbar('警告', '数据修复完成，但仍未找到小说数据。可能需要重新创建数据。');
      }

    } catch (e) {
      Get.back(); // 关闭加载对话框
      print('❌ 数据修复失败: $e');
      Get.snackbar('错误', '数据修复失败: $e');
    }
  }

  /// 直接从SharedPreferences加载数据
  Future<void> _directLoadFromStorage(NovelController novelController) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final allKeys = prefs.getKeys();
      final novelKeys = allKeys.where((key) => key.startsWith('novel_')).toList();

      print('🔧 找到 ${novelKeys.length} 个小说键，开始直接加载...');

      for (final key in novelKeys) {
        try {
          final data = prefs.getString(key);
          if (data != null) {
            final novelData = jsonDecode(data);
            final novel = Novel.fromJson(novelData);
            novelController.novels.add(novel);
            print('✅ 成功加载小说: ${novel.title}');
          }
        } catch (e) {
          print('⚠️ 加载小说失败 ($key): $e');
        }
      }

      print('🔧 直接加载完成，总计: ${novelController.novels.length} 本小说');

    } catch (e) {
      print('❌ 直接加载失败: $e');
    }
  }

  /// 获取当前用户Token
  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token');

    if (token != null) {
      print('🔍 获取到Token: ${token.length > 20 ? '${token.substring(0, 20)}...' : token}');

      // 检查token是否过期
      try {
        final isExpired = JwtDecoder.isExpired(token);
        print('🔍 Token是否过期: $isExpired');
        if (isExpired) {
          print('⚠️ Token已过期，需要重新登录');
          return null;
        }
      } catch (e) {
        print('⚠️ Token格式验证失败: $e');
        return null;
      }
    } else {
      print('❌ 未找到Token，用户可能未登录');
    }

    return token;
  }

  /// 分批上传数据
  Future<bool> _uploadDataInBatches(Map<String, dynamic> localData, String token) async {
    try {
      final timestamp = DateTime.now().toIso8601String();

      // 1. 分批上传小说数据
      final novels = localData['novels'] as List? ?? [];
      if (novels.isNotEmpty) {
        print('开始分批上传 ${novels.length} 本小说...');
        final success = await _uploadNovelsInBatches(novels, token, timestamp);
        if (!success) return false;
      }

      // 2. 上传知识库文档
      final knowledgeDocuments = localData['knowledgeDocuments'] as List? ?? [];
      if (knowledgeDocuments.isNotEmpty) {
        print('上传 ${knowledgeDocuments.length} 个知识库文档...');
        final compressedDocs = _compressKnowledgeDocuments(knowledgeDocuments);
        final success = await _uploadSingleDataType('knowledgeDocuments', compressedDocs, token, timestamp);
        if (!success) return false;
      }

      // 3. 上传角色卡片
      final characterCards = localData['characterCards'] as List? ?? [];
      if (characterCards.isNotEmpty) {
        print('上传 ${characterCards.length} 个角色卡片...');
        final success = await _uploadSingleDataType('characterCards', characterCards, token, timestamp);
        if (!success) return false;
      }

      // 4. 上传角色类型
      final characterTypes = localData['characterTypes'] as List? ?? [];
      if (characterTypes.isNotEmpty) {
        print('上传 ${characterTypes.length} 个角色类型...');
        final success = await _uploadSingleDataType('characterTypes', characterTypes, token, timestamp);
        if (!success) return false;
      }

      // 5. 上传风格包
      final stylePackages = localData['stylePackages'] as List? ?? [];
      if (stylePackages.isNotEmpty) {
        print('上传 ${stylePackages.length} 个风格包...');
        final success = await _uploadSingleDataType('stylePackages', stylePackages, token, timestamp);
        if (!success) return false;
      }

      // 6. 上传用户设置
      final userSettings = localData['userSettings'];
      if (userSettings != null) {
        print('上传用户设置...');
        final success = await _uploadSingleDataType('userSettings', userSettings, token, timestamp);
        if (!success) return false;
      }

      print('所有数据上传完成！');
      return true;
    } catch (e) {
      print('分批上传失败: $e');
      return false;
    }
  }

  /// 使用新的CloudBase同步服务上传数据
  Future<bool> _uploadNovelsInBatches(List novels, String token, String timestamp) async {
    print('🚀 开始使用CloudBase同步服务上传数据...');
    print('📊 小说数量: ${novels.length} 本');

    try {
      // 获取用户ID
      final user = _authService.currentUser.value;
      if (user == null) {
        print('❌ 用户未登录');
        return false;
      }

      // 收集所有数据
      final allData = await _collectAllUserData();
      if (allData == null) {
        print('❌ 收集用户数据失败');
        return false;
      }

      // 使用新的混合同步服务
      final hybridSyncService = Get.find<HybridSyncService>();

      // 检查服务是否已初始化
      if (!hybridSyncService.isInitialized.value) {
        print('❌ 混合同步服务未初始化');
        Get.snackbar('同步失败', '同步服务未就绪，请稍后重试');
        return false;
      }

      // 使用新的同步服务上传数据
      print('🚀 开始使用混合同步服务上传数据...');
      final success = await hybridSyncService.syncAllData();

      if (success) {
        print('🎉 混合同步服务数据同步成功！');
        Get.snackbar(
          '同步成功',
          '数据同步成功，所有数据已安全上传到云端',
          backgroundColor: Get.theme.primaryColor.withOpacity(0.1),
          colorText: Get.theme.primaryColor,
          duration: Duration(seconds: 3),
        );
        return true;
      } else {
        print('❌ 混合同步服务数据同步失败');
        Get.snackbar(
          '同步失败',
          '数据同步失败，请稍后重试',
          backgroundColor: Get.theme.colorScheme.error.withOpacity(0.1),
          colorText: Get.theme.colorScheme.error,
          duration: Duration(seconds: 5),
        );
        return false;
      }

    } catch (e) {
      print('❌ CloudBase数据同步异常: $e');
      CloudBaseSyncService.instance.showErrorMessage('数据同步异常: $e');
      return false;
    }
  }

  /// 智能分批上传
  Future<bool> _uploadInSmartBatches(List<Map<String, dynamic>> novelSizes, String token, String timestamp, int maxBatchSizeBytes) async {
    final batches = <List<Map<String, dynamic>>>[];
    List<Map<String, dynamic>> currentBatch = [];
    int currentBatchSize = 0;

    // 智能分批算法
    for (final novelInfo in novelSizes) {
      final novelSize = novelInfo['size'] as int;

      // 如果单个小说就超过限制，需要特殊处理
      if (novelSize > maxBatchSizeBytes) {
        // 先保存当前批次
        if (currentBatch.isNotEmpty) {
          batches.add(List.from(currentBatch));
          currentBatch.clear();
          currentBatchSize = 0;
        }

        // 超大小说需要压缩处理
        print('⚠️ 超大小说需要压缩: "${novelInfo['title']}" - ${(novelSize/1024/1024).toFixed(2)} MB');
        final compressedNovel = _compressLargeNovel(novelInfo['novel']);
        batches.add([{'novel': compressedNovel, 'size': jsonEncode(compressedNovel).length, 'title': novelInfo['title']}]);
        continue;
      }

      // 检查加入当前批次是否会超限
      if (currentBatchSize + novelSize > maxBatchSizeBytes && currentBatch.isNotEmpty) {
        batches.add(List.from(currentBatch));
        currentBatch.clear();
        currentBatchSize = 0;
      }

      currentBatch.add(novelInfo);
      currentBatchSize += novelSize;
    }

    // 添加最后一个批次
    if (currentBatch.isNotEmpty) {
      batches.add(currentBatch);
    }

    print('📦 智能分批结果: ${batches.length} 个批次');
    for (int i = 0; i < batches.length; i++) {
      final batch = batches[i];
      final batchSize = batch.fold<int>(0, (sum, novel) => sum + (novel['size'] as int));
      print('   批次 ${i + 1}: ${batch.length} 本小说 - ${(batchSize/1024/1024).toFixed(2)} MB');
    }

    // 逐批上传
    for (int i = 0; i < batches.length; i++) {
      final batch = batches[i];
      final novels = batch.map((info) => info['novel']).toList();

      final success = await _uploadDirectToCloudStorage({'novels': novels}, token);
      if (!success) {
        print('❌ 批次 ${i + 1} 上传失败，停止后续上传');
        return false;
      }

      print('✅ 批次 ${i + 1}/${batches.length} 上传成功');

      // 批次间延迟
      if (i < batches.length - 1) {
        await Future.delayed(Duration(milliseconds: 1000));
      }
    }

    print('🎉 所有 ${novelSizes.length} 本小说已成功分批上传到云端！');
    return true;
  }

  /// 压缩超大小说
  Map<String, dynamic> _compressLargeNovel(Map<String, dynamic> novel) {
    final compressed = Map<String, dynamic>.from(novel);

    // 压缩内容
    if (compressed['content'] != null && compressed['content'].length > 10000) {
      compressed['content'] = compressed['content'].substring(0, 10000) + '...[内容已压缩]';
    }

    // 压缩章节
    if (compressed['chapters'] != null && compressed['chapters'] is List) {
      final chapters = compressed['chapters'] as List;
      compressed['chapters'] = chapters.take(5).map((chapter) {
        if (chapter['content'] != null && chapter['content'].length > 5000) {
          chapter['content'] = chapter['content'].substring(0, 5000) + '...[章节内容已压缩]';
        }
        return chapter;
      }).toList();
    }

    return compressed;
  }

  /// 直接上传到云存储
  Future<bool> _uploadDirectToCloudStorage(Map<String, dynamic> data, String token) async {
    try {
      final dataString = jsonEncode(data);
      final dataSizeMB = dataString.length / (1024 * 1024);
      print('🚀 开始上传批次数据 - ${dataSizeMB.toFixed(2)} MB');

      final response = await _dio.post(
        '${ApiConfig.baseUrl}/sync/upload-direct',
        data: {
          'data': data,
          'timestamp': DateTime.now().toIso8601String(),
        },
        options: dio_pkg.Options(
          headers: {'Authorization': 'Bearer $token'},
          sendTimeout: Duration(minutes: 5),
          receiveTimeout: Duration(minutes: 5),
        ),
      );

      if (response.data['success'] == true) {
        print('✅ 批次上传成功: ${response.data['message']}');
        return true;
      } else {
        print('❌ 批次上传失败: ${response.data['message']}');
        return false;
      }
    } catch (e) {
      print('❌ 批次上传异常: $e');
      if (e.toString().contains('413')) {
        print('❌ 数据仍然太大，需要进一步压缩');
      }
      return false;
    }
  }



  /// 上传单一数据类型
  Future<bool> _uploadSingleDataType(String dataType, dynamic data, String token, String timestamp) async {
    try {
      final response = await _dio.post(
        '${ApiConfig.baseUrl}/sync/upload',
        data: {
          'dataType': dataType,
          'data': {dataType: data},
          'batchInfo': {
            'isComplete': true,
            'batchId': null,
            'batchIndex': 0,
            'totalBatches': 1,
          },
          'timestamp': timestamp,
        },
        options: dio_pkg.Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );

      if (response.data['success'] == true) {
        print('✅ ${response.data['message']}');
        return true;
      } else {
        print('❌ $dataType 上传失败: ${response.data['message']}');
        return false;
      }
    } catch (e) {
      print('❌ $dataType 上传异常: $e');
      return false;
    }
  }

  /// 压缩小说数据（已禁用 - 直接返回原始数据）
  List<Map<String, dynamic>> _compressNovels(List novels) {
    print('⚠️ 压缩函数已禁用，返回完整数据');
    // 直接返回原始数据，不进行任何压缩
    return novels.map<Map<String, dynamic>>((novel) {
      if (novel is Map<String, dynamic>) {
        return novel;
      } else {
        // 如果是Novel对象，转换为Map
        return novel.toJson();
      }
    }).toList();
  }

  /// 创建最小化小说数据（只保留ID和标题）
  List<Map<String, dynamic>> _createMinimalNovels(List novels) {
    return novels.map<Map<String, dynamic>>((novel) {
      return {
        'id': novel['id'] ?? 'unknown',
        'title': novel['title'] ?? '未命名',
        'author': novel['author'] ?? '未知',
        'content': '...',
        'chapters': [],
        'createdAt': novel['createdAt'],
        'updatedAt': novel['updatedAt'],
      };
    }).toList();
  }

  /// 压缩知识库文档数据（已禁用 - 直接返回原始数据）
  List<Map<String, dynamic>> _compressKnowledgeDocuments(List documents) {
    print('⚠️ 知识库文档压缩函数已禁用，返回完整数据');
    // 直接返回原始数据，不进行任何压缩
    return documents.map<Map<String, dynamic>>((doc) {
      if (doc is Map<String, dynamic>) {
        return doc;
      } else {
        // 如果是其他对象，尝试转换为Map
        return doc.toJson();
      }
    }).toList();
  }





  /// 确保必要的控制器已注册
  void _ensureControllersRegistered() {
    try {
      // 确保角色卡片控制器注册
      if (!Get.isRegistered<CharacterCardController>()) {
        print('🔧 注册CharacterCardController...');
        Get.put(CharacterCardController());
      }

      // 确保角色类型控制器注册
      if (!Get.isRegistered<CharacterTypeController>()) {
        print('🔧 注册CharacterTypeController...');
        Get.put(CharacterTypeController());
      }

      print('✅ 所有必要控制器已确保注册');
    } catch (e) {
      print('⚠️ 控制器注册失败: $e');
    }
  }

  /// 收集所有用户数据（公共方法）
  Future<Map<String, dynamic>?> collectAllUserData() async {
    return await _collectAllUserData();
  }

  /// 收集所有用户数据
  Future<Map<String, dynamic>?> _collectAllUserData() async {
    try {
      print('📊 收集所有用户数据...');

      // 使用现有的收集本地数据方法
      final localData = await _collectLocalData();

      // 添加版本信息和时间戳
      final allData = {
        ...localData,
        'timestamp': DateTime.now().toIso8601String(),
        'version': '1.0.0',
      };

      // 计算数据大小
      final dataString = jsonEncode(allData);
      final dataSizeMB = dataString.length / (1024 * 1024);
      print('📊 总数据大小: ${dataSizeMB.toStringAsFixed(2)} MB');

      return allData;

    } catch (e) {
      print('❌ 收集用户数据异常: $e');
      return null;
    }
  }
}

extension DoubleExtension on double {
  String toFixed(int digits) => toStringAsFixed(digits);
}
